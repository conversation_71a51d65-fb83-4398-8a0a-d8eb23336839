#!/usr/bin/env python3
"""
测试多边形正方形分解算法
"""

import math
from itertools import combinations

def calculate_distance(point1, point2):
    """计算两点之间的欧几里得距离"""
    return math.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)

def are_lines_parallel(line1, line2, tolerance=0.1):
    """判断两条线是否平行"""
    p1, p2 = line1
    p3, p4 = line2
    
    # 计算两条线的方向向量
    v1 = (p2[0] - p1[0], p2[1] - p1[1])
    v2 = (p4[0] - p3[0], p4[1] - p3[1])
    
    # 避免零向量
    if (v1[0]**2 + v1[1]**2) < 1e-10 or (v2[0]**2 + v2[1]**2) < 1e-10:
        return False
    
    # 计算叉积，如果为0则平行
    cross_product = v1[0] * v2[1] - v1[1] * v2[0]
    
    # 计算向量长度
    len1 = math.sqrt(v1[0]**2 + v1[1]**2)
    len2 = math.sqrt(v2[0]**2 + v2[1]**2)
    
    # 归一化的叉积
    normalized_cross = abs(cross_product) / (len1 * len2)
    
    return normalized_cross < tolerance

def lines_overlap(line1, line2):
    """检查两条线是否有重叠的点"""
    return (line1[0] in line2 or line1[1] in line2 or 
            line2[0] in line1 or line2[1] in line1)

def calculate_parallel_distance(line1, line2):
    """计算两条平行线之间的垂直距离"""
    p1, p2 = line1
    p3, p4 = line2
    
    # 使用点到直线的距离公式
    # 直线方程：ax + by + c = 0
    # 从line1构建直线方程
    a = p2[1] - p1[1]
    b = p1[0] - p2[0]
    c = p2[0] * p1[1] - p1[0] * p2[1]
    
    # 计算line2上的点到line1的距离
    dist1 = abs(a * p3[0] + b * p3[1] + c) / math.sqrt(a*a + b*b)
    dist2 = abs(a * p4[0] + b * p4[1] + c) / math.sqrt(a*a + b*b)
    
    # 返回平均距离
    return (dist1 + dist2) / 2

def point_in_polygon(point, polygon_vertices):
    """判断点是否在多边形内（射线法）"""
    x, y = point
    n = len(polygon_vertices)
    inside = False
    
    p1x, p1y = polygon_vertices[0]
    for i in range(1, n + 1):
        p2x, p2y = polygon_vertices[i % n]
        if y > min(p1y, p2y):
            if y <= max(p1y, p2y):
                if x <= max(p1x, p2x):
                    if p1y != p2y:
                        xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                    if p1x == p2x or x <= xinters:
                        inside = not inside
        p1x, p1y = p2x, p2y
    
    return inside

def test_square_decomposition():
    """测试正方形分解算法"""
    
    # 测试用例1：简单的L形多边形（由两个正方形组成）
    print("=== 测试用例1：L形多边形 ===")
    l_shape_vertices = [
        (0, 0), (50, 0), (50, 50), (100, 50), 
        (100, 100), (0, 100)
    ]
    
    print(f"多边形顶点: {l_shape_vertices}")
    
    # 生成所有4点组合
    all_4point_combinations = list(combinations(l_shape_vertices, 4))
    print(f"生成 {len(all_4point_combinations)} 个4点组合")
    
    found_squares = []
    
    for four_points in all_4point_combinations:
        # 生成所有可能的2点组合（6条可能的线）
        all_lines = list(combinations(four_points, 2))
        
        # 找出所有平行线对
        parallel_pairs = []
        for j in range(len(all_lines)):
            for k in range(j+1, len(all_lines)):
                line1 = all_lines[j]
                line2 = all_lines[k]
                
                # 检查是否平行且不重叠
                if are_lines_parallel(line1, line2, 0.1) and not lines_overlap(line1, line2):
                    parallel_pairs.append((line1, line2))
        
        # 对每对平行线，检查是否能形成正方形
        for line1, line2 in parallel_pairs:
            # 计算平行边的距离
            side_length = calculate_parallel_distance(line1, line2)
            
            # 检查是否所有点都在原多边形内
            all_points_inside = True
            for point in four_points:
                if not point_in_polygon(point, l_shape_vertices):
                    all_points_inside = False
                    break
            
            if all_points_inside and side_length > 10:  # 最小边长阈值
                found_squares.append({
                    'vertices': list(four_points),
                    'side_length': side_length,
                    'parallel_lines': (line1, line2)
                })
                print(f"  找到正方形: 边长={side_length:.1f}, 顶点={four_points}")
    
    print(f"总共找到 {len(found_squares)} 个正方形")
    
    if found_squares:
        min_side_length = min(square['side_length'] for square in found_squares)
        print(f"最小边长: {min_side_length:.1f}")
    
    print()

if __name__ == "__main__":
    test_square_decomposition()
