#!/usr/bin/env python3
"""
测试多边形正方形分解算法的核心逻辑
"""

import math
from itertools import combinations

# 参数设置
square_inside_polygon_threshold = 0.95
square_parallel_tolerance = 0.1

def calculate_distance(point1, point2):
    """计算两点之间的欧几里得距离"""
    return math.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)

def are_lines_parallel(line1, line2, tolerance=0.1):
    """判断两条线是否平行"""
    p1, p2 = line1
    p3, p4 = line2
    
    # 计算两条线的方向向量
    v1 = (p2[0] - p1[0], p2[1] - p1[1])
    v2 = (p4[0] - p3[0], p4[1] - p3[1])
    
    # 避免零向量
    if (v1[0]**2 + v1[1]**2) < 1e-10 or (v2[0]**2 + v2[1]**2) < 1e-10:
        return False
    
    # 计算叉积，如果为0则平行
    cross_product = v1[0] * v2[1] - v1[1] * v2[0]
    
    # 计算向量长度
    len1 = math.sqrt(v1[0]**2 + v1[1]**2)
    len2 = math.sqrt(v2[0]**2 + v2[1]**2)
    
    # 归一化的叉积
    normalized_cross = abs(cross_product) / (len1 * len2)
    
    return normalized_cross < tolerance

def lines_overlap(line1, line2):
    """检查两条线是否有重叠的点"""
    return (line1[0] in line2 or line1[1] in line2 or 
            line2[0] in line1 or line2[1] in line1)

def calculate_parallel_distance(line1, line2):
    """计算两条平行线之间的垂直距离"""
    p1, p2 = line1
    p3, p4 = line2

    # 使用点到直线的距离公式
    a = p2[1] - p1[1]
    b = p1[0] - p2[0]
    c = p2[0] * p1[1] - p1[0] * p2[1]

    # 避免除零错误
    denominator = math.sqrt(a*a + b*b)
    if denominator < 1e-10:
        # 如果两点重合，返回两线段中点之间的距离
        mid1 = ((p1[0] + p2[0])/2, (p1[1] + p2[1])/2)
        mid2 = ((p3[0] + p4[0])/2, (p3[1] + p4[1])/2)
        return calculate_distance(mid1, mid2)

    # 计算line2上的点到line1的距离
    dist1 = abs(a * p3[0] + b * p3[1] + c) / denominator
    dist2 = abs(a * p4[0] + b * p4[1] + c) / denominator

    # 返回平均距离
    return (dist1 + dist2) / 2

def point_in_polygon(point, polygon_vertices):
    """判断点是否在多边形内（射线法）"""
    x, y = point
    n = len(polygon_vertices)
    inside = False
    
    p1x, p1y = polygon_vertices[0]
    for i in range(1, n + 1):
        p2x, p2y = polygon_vertices[i % n]
        if y > min(p1y, p2y):
            if y <= max(p1y, p2y):
                if x <= max(p1x, p2x):
                    if p1y != p2y:
                        xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                    if p1x == p2x or x <= xinters:
                        inside = not inside
        p1x, p1y = p2x, p2y
    
    return inside

def is_quadrilateral_inside_polygon_with_threshold(quad_vertices, polygon_vertices, threshold=0.95):
    """判断四边形是否在多边形内部（按阈值比例）"""
    
    total_points = 0
    inside_points = 0
    
    # 检查四边形的所有顶点
    for vertex in quad_vertices:
        total_points += 1
        if point_in_polygon(vertex, polygon_vertices):
            inside_points += 1
    
    # 检查四边形的所有边上的采样点
    for i in range(4):
        edge_start = quad_vertices[i]
        edge_end = quad_vertices[(i+1) % 4]
        
        # 在边上采样多个点进行检查
        for t in [0.2, 0.4, 0.6, 0.8]:
            sample_point = (
                edge_start[0] + t * (edge_end[0] - edge_start[0]),
                edge_start[1] + t * (edge_end[1] - edge_start[1])
            )
            total_points += 1
            if point_in_polygon(sample_point, polygon_vertices):
                inside_points += 1
    
    # 计算在内部的比例
    inside_ratio = inside_points / total_points if total_points > 0 else 0
    
    return inside_ratio >= threshold

def find_squares_by_point_combination(polygon_vertices, tolerance=0.1):
    """通过点组合方法寻找正方形"""
    
    print(f"开始分析多边形，共有 {len(polygon_vertices)} 个顶点")
    
    # 步骤1：生成所有4点组合
    all_4point_combinations = list(combinations(polygon_vertices, 4))
    print(f"生成 {len(all_4point_combinations)} 个4点组合")
    
    found_squares = []
    
    for four_points in all_4point_combinations:
        # 步骤2：生成这4个点的所有2点组合（6条可能的线）
        all_lines = list(combinations(four_points, 2))
        
        # 步骤3：找出所有平行线对
        parallel_pairs = []
        for j in range(len(all_lines)):
            for k in range(j+1, len(all_lines)):
                line1 = all_lines[j]
                line2 = all_lines[k]
                
                # 检查是否平行且不重叠
                if are_lines_parallel(line1, line2, tolerance) and not lines_overlap(line1, line2):
                    parallel_pairs.append((line1, line2))
        
        # 步骤4：对每对平行线，构建四边形
        for line1, line2 in parallel_pairs:
            quadrilateral = list(four_points)

            if quadrilateral:
                # 步骤5：判断四边形是否在原多边形内
                if is_quadrilateral_inside_polygon_with_threshold(
                    quadrilateral, polygon_vertices, square_inside_polygon_threshold):

                    # 步骤6：计算平行边的垂直距离（边长）
                    side_length = calculate_parallel_distance(line1, line2)

                    # 只保留边长大于阈值的正方形
                    if side_length > 5:  # 最小边长阈值
                        found_squares.append({
                            'vertices': quadrilateral,
                            'side_length': side_length,
                            'parallel_lines': (line1, line2)
                        })
                        print(f"  找到正方形 #{len(found_squares)}: 边长={side_length:.1f}px")
                        print(f"    平行线1: {line1}")
                        print(f"    平行线2: {line2}")
                        print(f"    四边形顶点: {quadrilateral}")
    
    # 步骤7：找出最小边长
    if found_squares:
        min_side_length = min(square['side_length'] for square in found_squares)
        print(f"\n总共找到 {len(found_squares)} 个正方形")
        print(f"最小边长: {min_side_length:.1f}px")
        return found_squares, min_side_length
    
    print("未找到符合条件的正方形")
    return [], 0

def test_algorithm():
    """测试算法"""

    # 测试用例1：简单的正方形
    print("=== 测试用例1：简单正方形 ===")
    square_vertices = [
        (0, 0), (50, 0), (50, 50), (0, 50)
    ]

    print(f"多边形顶点: {square_vertices}")
    squares, min_side_length = find_squares_by_point_combination(square_vertices, square_parallel_tolerance)

    print("\n" + "="*50)

    # 测试用例2：L形多边形（由两个正方形组成）
    print("=== 测试用例2：L形多边形 ===")
    l_shape_vertices = [
        (0, 0), (50, 0), (50, 50), (100, 50),
        (100, 100), (0, 100)
    ]

    print(f"多边形顶点: {l_shape_vertices}")
    squares2, min_side_length2 = find_squares_by_point_combination(l_shape_vertices, square_parallel_tolerance)

    print("\n" + "="*50)

    # 测试用例3：包含明确正方形的多边形
    print("=== 测试用例3：包含正方形的多边形 ===")
    polygon_with_square = [
        (10, 10), (40, 10), (40, 40), (10, 40),  # 内部正方形
        (0, 0), (50, 0), (50, 50), (0, 50)       # 外部正方形
    ]

    print(f"多边形顶点: {polygon_with_square}")
    squares3, min_side_length3 = find_squares_by_point_combination(polygon_with_square, square_parallel_tolerance)

if __name__ == "__main__":
    test_algorithm()
